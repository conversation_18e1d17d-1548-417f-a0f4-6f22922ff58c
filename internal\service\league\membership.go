package league

import (
	"context"
	"fmt"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
)

func (s *service) InviteLeague(ctx context.Context, leagueID string, userID string) (*league.League, error) {
	// First check if the user has access to the league
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return nil, err
	}

	// Check if the user is a member of the league
	isMember := false
	for _, member := range l.Members {
		if member.UserID == userID {
			isMember = true
			break
		}
	}

	if !isMember {
		return nil, errors.New(errors.Service, "user_not_league_member", errors.Forbidden, nil)
	}

	// Return the league with the invite code
	return l, nil
}

func (s *service) JoinLeague(ctx context.Context, userID string, userName string, photoURL string, inviteCode string) (*league.League, error) {
	if inviteCode == "" {
		return nil, errors.New(errors.Service, "invite_code_required", errors.Validation, nil)
	}
	if userID == "" {
		return nil, errors.New(errors.Service, "user_id_required_for_join", errors.Validation, nil)
	}

	l, err := s.Repository.FindByInviteCode(ctx, inviteCode)
	if err != nil {
		return nil, err
	}

	// Check if user is already a member
	for _, member := range l.Members {
		if member.UserID == userID {
			return nil, errors.New(errors.Service, "user_already_in_league", errors.Conflict, nil)
		}
	}

	newMember := league.LeagueMember{
		UserID:                 userID,
		UserName:               userName,
		PhotoURL:               photoURL, // Changed from AvatarURL to PhotoURL
		JoinedAt:               time.Now(),
		TransactionStreak:      0,
		CurrentLeagueLevel:     league.BronzeLevel,
		LastStreakActivityDate: time.Time{},
	}
	l.Members = append(l.Members, newMember)

	if err := s.Repository.Update(ctx, l); err != nil {
		return nil, err
	}
	return l, nil
}

func (s *service) LeaveLeague(ctx context.Context, leagueID string, userID string) error {
	// First check if the user has access to the league
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return err
	}

	if l.OwnerUserID == userID {
		return errors.New(errors.Service, "owner_cannot_leave_league", errors.BadRequest, fmt.Errorf("owner must delete or transfer ownership"))
	}

	memberIndex := -1
	for i, member := range l.Members {
		if member.UserID == userID {
			memberIndex = i
			break
		}
	}

	if memberIndex == -1 {
		return errors.New(errors.Service, "user_not_in_league_to_leave", errors.NotFound, nil)
	}

	l.Members = append(l.Members[:memberIndex], l.Members[memberIndex+1:]...)

	return s.Repository.Update(ctx, l)
}
