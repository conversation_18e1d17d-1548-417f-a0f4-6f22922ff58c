package league

import (
	"context"
	"log"
	"sort"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
)

func (s *service) StartNewSeasonInLeague(ctx context.Context, leagueID string, userID string, seasonName string, startDate time.Time, endDate time.Time) (*league.League, error) {
	// First check if the user has access to the league
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return nil, err
	}

	// Then check if the user is the owner (only owners can start a new season)
	if l.OwnerUserID != userID {
		return nil, errors.New(errors.Service, "user_not_league_owner_for_new_season", errors.Forbidden, nil)
	}

	if startDate.IsZero() || endDate.IsZero() || endDate.Before(startDate) {
		return nil, errors.New(errors.Service, "invalid_season_dates", errors.Validation, nil)
	}
	if seasonName == "" {
		return nil, errors.New(errors.Service, "season_name_required", errors.Validation, nil)
	}

	for i := range l.Members {
		l.Members[i].TransactionStreak = 0
		l.Members[i].LastStreakActivityDate = time.Time{}
		l.Members[i].CurrentLeagueLevel = league.BronzeLevel
	}

	l.CurrentSeason = league.Season{
		Name:      seasonName,
		StartDate: startDate,
		EndDate:   endDate,
	}

	if err := s.Repository.Update(ctx, l); err != nil {
		return nil, err
	}
	return l, nil
}

func (s *service) RecordTransactionForAllUserLeagues(ctx context.Context, userID string, transactionDate time.Time) error {
	userLeagues, err := s.Repository.FindAllLeagues(ctx, userID)
	if err != nil {
		return err
	}

	year, month, day := transactionDate.Date()
	normalizedTransactionDate := time.Date(year, month, day, 0, 0, 0, 0, transactionDate.Location())

	for _, l := range userLeagues {
		memberUpdated := false
		for i, member := range l.Members {
			if member.UserID == userID {
				lyear, lmonth, lday := member.LastStreakActivityDate.Date()
				normalizedLastStreakActivityDate := time.Date(lyear, lmonth, lday, 0, 0, 0, 0, member.LastStreakActivityDate.Location())

				if normalizedLastStreakActivityDate.Equal(normalizedTransactionDate) ||
					transactionDate.Before(l.CurrentSeason.StartDate) ||
					transactionDate.After(l.CurrentSeason.EndDate) {
					continue
				}

				l.Members[i].TransactionStreak++
				l.Members[i].LastStreakActivityDate = normalizedTransactionDate

				sortedRequirements := make([]league.LevelRequirement, len(l.LevelRequirements))
				copy(sortedRequirements, l.LevelRequirements)
				sort.Slice(sortedRequirements, func(k, j int) bool {
					return sortedRequirements[k].TransactionStreakNeeded > sortedRequirements[j].TransactionStreakNeeded
				})

				newLevel := l.Members[i].CurrentLeagueLevel
				for _, req := range sortedRequirements {
					if l.Members[i].TransactionStreak >= req.TransactionStreakNeeded {
						newLevel = req.Level
						break
					}
				}
				l.Members[i].CurrentLeagueLevel = newLevel
				memberUpdated = true
				break
			}
		}
		if memberUpdated {
			if err := s.Repository.Update(ctx, l); err != nil {
				log.Printf("Error updating league %s for user %s transaction streak: %v", l.ID, userID, err)
			}
		}
	}
	return nil
}

func (s *service) FindLeagueRanking(ctx context.Context, leagueID string, userID string) (*league.LeagueRanking, error) {
	// First check if the user has access to the league
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return nil, err
	}

	rankings := make([]*league.UserRanking, len(l.Members))
	for i, member := range l.Members {
		rankings[i] = &league.UserRanking{
			UserID:                 member.UserID,
			UserName:               member.UserName,
			CurrentLeagueLevel:     member.CurrentLeagueLevel,
			TotalTransactionStreak: member.TransactionStreak,
		}
	}

	sort.Slice(rankings, func(i, j int) bool {
		if rankings[i].TotalTransactionStreak != rankings[j].TotalTransactionStreak {
			return rankings[i].TotalTransactionStreak > rankings[j].TotalTransactionStreak
		}
		return rankings[i].UserName < rankings[j].UserName
	})

	leagueRanking := &league.LeagueRanking{}
	if len(rankings) > 3 {
		leagueRanking.Podium = rankings[:3]
		generalLimit := 20
		if len(rankings[3:]) < generalLimit {
			generalLimit = len(rankings[3:])
		}
		leagueRanking.General = rankings[3 : 3+generalLimit]
	} else {
		leagueRanking.Podium = rankings
		leagueRanking.General = []*league.UserRanking{}
	}

	return leagueRanking, nil
}
