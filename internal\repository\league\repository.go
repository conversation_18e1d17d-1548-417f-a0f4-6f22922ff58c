package league

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	Find(ctx context.Context, id primitive.ObjectID) (*league.League, error)
	FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error)                    // Leagues a user is a member of or owns
	FindByID(ctx context.Context, leagueID string) (*league.League, error)                          // Convenience for string ID
	FindByIDLoggedUser(ctx context.Context, leagueID string, userID string) (*league.League, error) // Find league by ID with user access check
	FindByInviteCode(ctx context.Context, inviteCode string) (*league.League, error)
}

type Writer interface {
	Create(ctx context.Context, l *league.League) (string, error)
	Update(ctx context.Context, l *league.League) error // For all league updates, including member changes
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
